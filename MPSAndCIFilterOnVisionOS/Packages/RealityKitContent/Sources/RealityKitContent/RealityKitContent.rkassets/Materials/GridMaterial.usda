#usda 1.0
(
    defaultPrim = "Root"
    metersPerUnit = 1
    upAxis = "Y"
)

def Xform "Root"
{
    def Material "GridMaterial"
    {
        reorder nameChildren = ["", "", "", "", "", "", "", "", "", "", "", "", "", "", "DefaultSurfaceShader", "MaterialXPreviewSurface", "Texcoord", "Add", "Multiply", "Fractional", "LineCounts", "Multiply_1", "Separate2", "Separate2_1", "Ifgreater", "Ifgreater_1", "Max", "Background_Color"]
        token outputs:mtlx:surface.connect = </Root/GridMaterial/MaterialXPreviewSurface.outputs:out>
        token outputs:realitykit:vertex
        token outputs:surface
        float2 ui:nodegraph:realitykit:subgraphOutputs:pos = (2222, 300.5)
        float2 ui:nodegraph:realitykit:subgraphOutputs:size = (182, 89)
        int ui:nodegraph:realitykit:subgraphOutputs:stackingOrder = 749

        def Shader "DefaultSurfaceShader"
        {
            uniform token info:id = "UsdPreviewSurface"
            color3f inputs:diffuseColor = (1, 1, 1)
            float inputs:roughness = 0.75
            token outputs:surface
        }

        def Shader "MaterialXPreviewSurface"
        {
            uniform token info:id = "ND_UsdPreviewSurface_surfaceshader"
            float inputs:clearcoat
            float inputs:clearcoatRoughness
            color3f inputs:diffuseColor.connect = </Root/GridMaterial/Remap.outputs:out>
            color3f inputs:emissiveColor
            float inputs:ior
            float inputs:metallic = 0.15
            float3 inputs:normal
            float inputs:occlusion
            float inputs:opacity
            float inputs:opacityThreshold
            float inputs:roughness = 0.5
            token outputs:out
            float2 ui:nodegraph:node:pos = (1967, 300.5)
            float2 ui:nodegraph:node:size = (208, 297)
            int ui:nodegraph:node:stackingOrder = 870
            string[] ui:nodegraph:realitykit:node:attributesShowingChildren = ["Advanced"]
        }

        def Shader "Texcoord"
        {
            uniform token info:id = "ND_texcoord_vector2"
            float2 outputs:out
            float2 ui:nodegraph:node:pos = (94.14453, 35.29297)
            float2 ui:nodegraph:node:size = (182, 43)
            int ui:nodegraph:node:stackingOrder = 1358
        }

        def Shader "Multiply"
        {
            uniform token info:id = "ND_multiply_vector2"
            float2 inputs:in1.connect = </Root/GridMaterial/Texcoord.outputs:out>
            float2 inputs:in2 = (32, 15)
            float2 inputs:in2.connect = </Root/GridMaterial/LineCounts.outputs:out>
            float2 outputs:out
            float2 ui:nodegraph:node:pos = (275.64453, 47.29297)
            float2 ui:nodegraph:node:size = (61, 36)
            int ui:nodegraph:node:stackingOrder = 1348
            string[] ui:nodegraph:realitykit:node:attributesShowingChildren = ["inputs:in2"]
        }

        def Shader "Fractional"
        {
            uniform token info:id = "ND_realitykit_fractional_vector2"
            float2 inputs:in.connect = </Root/GridMaterial/Multiply.outputs:out>
            float2 outputs:out
            float2 ui:nodegraph:node:pos = (440.5, 49.5)
            float2 ui:nodegraph:node:size = (155, 99)
            int ui:nodegraph:node:stackingOrder = 1345
        }

        def Shader "BaseColor"
        {
            uniform token info:id = "ND_constant_color3"
            color3f inputs:value = (0.89737034, 0.89737034, 0.89737034) (
                colorSpace = "Input - Texture - sRGB - sRGB"
            )
            color3f inputs:value.connect = None
            color3f outputs:out
            float2 ui:nodegraph:node:pos = (1537.5977, 363.07812)
            float2 ui:nodegraph:node:size = (150, 43)
            int ui:nodegraph:node:stackingOrder = 1353
        }

        def Shader "LineColor"
        {
            uniform token info:id = "ND_constant_color3"
            color3f inputs:value = (0.55945957, 0.55945957, 0.55945957) (
                colorSpace = "Input - Texture - sRGB - sRGB"
            )
            color3f inputs:value.connect = None
            color3f outputs:out
            float2 ui:nodegraph:node:pos = (1536.9844, 287.86328)
            float2 ui:nodegraph:node:size = (146, 43)
            int ui:nodegraph:node:stackingOrder = 1355
        }

        def Shader "LineWidths"
        {
            uniform token info:id = "ND_combine2_vector2"
            float inputs:in1 = 0.1
            float inputs:in2 = 0.1
            float2 outputs:out
            float2 ui:nodegraph:node:pos = (443.64453, 233.79297)
            float2 ui:nodegraph:node:size = (151, 43)
            int ui:nodegraph:node:stackingOrder = 1361
        }

        def Shader "LineCounts"
        {
            uniform token info:id = "ND_combine2_vector2"
            float inputs:in1 = 24
            float inputs:in2 = 12
            float2 outputs:out
            float2 ui:nodegraph:node:pos = (94.14453, 138.29297)
            float2 ui:nodegraph:node:size = (153, 43)
            int ui:nodegraph:node:stackingOrder = 1359
        }

        def Shader "Remap"
        {
            uniform token info:id = "ND_remap_color3"
            color3f inputs:in.connect = </Root/GridMaterial/Combine3.outputs:out>
            color3f inputs:inhigh.connect = None
            color3f inputs:inlow.connect = None
            color3f inputs:outhigh.connect = </Root/GridMaterial/BaseColor.outputs:out>
            color3f inputs:outlow.connect = </Root/GridMaterial/LineColor.outputs:out>
            color3f outputs:out
            float2 ui:nodegraph:node:pos = (1755.5, 300.5)
            float2 ui:nodegraph:node:size = (95, 171)
            int ui:nodegraph:node:stackingOrder = 1282
            string[] ui:nodegraph:realitykit:node:attributesShowingChildren = ["inputs:outlow"]
        }

        def Shader "Separate2"
        {
            uniform token info:id = "ND_separate2_vector2"
            float2 inputs:in.connect = </Root/GridMaterial/Range.outputs:out>
            float outputs:outx
            float outputs:outy
            float2 ui:nodegraph:node:pos = (1212.6445, 128.91797)
            float2 ui:nodegraph:node:size = (116, 117)
            int ui:nodegraph:node:stackingOrder = 1363
        }

        def Shader "Combine3"
        {
            uniform token info:id = "ND_combine3_color3"
            float inputs:in1.connect = </Root/GridMaterial/Min.outputs:out>
            float inputs:in2.connect = </Root/GridMaterial/Min.outputs:out>
            float inputs:in3.connect = </Root/GridMaterial/Min.outputs:out>
            color3f outputs:out
            float2 ui:nodegraph:node:pos = (1578.1445, 128.91797)
            float2 ui:nodegraph:node:size = (146, 54)
            int ui:nodegraph:node:stackingOrder = 1348
        }

        def Shader "Range"
        {
            uniform token info:id = "ND_range_vector2"
            bool inputs:doclamp = 1
            float2 inputs:gamma = (2, 2)
            float2 inputs:in.connect = </Root/GridMaterial/Absval.outputs:out>
            float2 inputs:inhigh.connect = </Root/GridMaterial/LineWidths.outputs:out>
            float2 inputs:inlow = (0.02, 0.02)
            float2 inputs:outhigh
            float2 inputs:outlow
            float2 outputs:out
            float2 ui:nodegraph:node:pos = (990.64453, 128.91797)
            float2 ui:nodegraph:node:size = (98, 207)
            int ui:nodegraph:node:stackingOrder = 1364
        }

        def Shader "Subtract"
        {
            uniform token info:id = "ND_subtract_vector2"
            float2 inputs:in1.connect = </Root/GridMaterial/Fractional.outputs:out>
            float2 inputs:in2.connect = </Root/GridMaterial/LineWidths.outputs:out>
            float2 outputs:out
            float2 ui:nodegraph:node:pos = (612.64453, 87.04297)
            float2 ui:nodegraph:node:size = (63, 36)
            int ui:nodegraph:node:stackingOrder = 1348
        }

        def Shader "Absval"
        {
            uniform token info:id = "ND_absval_vector2"
            float2 inputs:in.connect = </Root/GridMaterial/Subtract.outputs:out>
            float2 outputs:out
            float2 ui:nodegraph:node:pos = (765.64453, 87.04297)
            float2 ui:nodegraph:node:size = (123, 43)
            int ui:nodegraph:node:stackingOrder = 1348
        }

        def Shader "Min"
        {
            uniform token info:id = "ND_min_float"
            float inputs:in1.connect = </Root/GridMaterial/Separate2.outputs:outx>
            float inputs:in2.connect = </Root/GridMaterial/Separate2.outputs:outy>
            float outputs:out
            float2 ui:nodegraph:node:pos = (1388.1445, 128.91797)
            float2 ui:nodegraph:node:size = (114, 36)
            int ui:nodegraph:node:stackingOrder = 1363
        }
    }
}

