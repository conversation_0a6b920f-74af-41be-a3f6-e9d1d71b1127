#usda 1.0
(
    defaultPrim = "Root"
    metersPerUnit = 1
    upAxis = "Y"
)

def Xform "Root"
{
    reorder nameChildren = ["GridMaterial", "Sphere"]
    rel material:binding = None (
        bindMaterialAs = "weakerThanDescendants"
    )

    def Sphere "Sphere" (
        active = true
        prepend apiSchemas = ["MaterialBindingAPI"]
    )
    {
        rel material:binding = </Root/GridMaterial/GridMaterial> (
            bindMaterialAs = "weakerThanDescendants"
        )
        double radius = 0.05
        quatf xformOp:orient = (1, 0, 0, 0)
        float3 xformOp:scale = (1, 1, 1)
        float3 xformOp:translate = (0, 0, 0.0004)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]

        def RealityKitComponent "Collider"
        {
            uint group = 1
            uniform token info:id = "RealityKit.Collider"
            uint mask = 4294967295
            token type = "Default"

            def RealityKitStruct "Shape"
            {
                float3 extent = (0.2, 0.2, 0.2)
                float radius = 0.05
                token shapeType = "Sphere"
            }
        }

        def RealityKitComponent "InputTarget"
        {
            uniform token info:id = "RealityKit.InputTarget"
        }
    }

    def "GridMaterial" (
        active = true
        prepend references = @Materials/GridMaterial.usda@
    )
    {
        float3 xformOp:scale = (1, 1, 1)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]
    }
}

