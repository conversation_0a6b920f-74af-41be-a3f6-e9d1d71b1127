#usda 1.0
(
    defaultPrim = "Root"
    metersPerUnit = 1
    upAxis = "Y"
)

def Xform "Root"
{
    reorder nameChildren = ["Sphere_Left", "Sphere_Right", "GridMaterial"]
    def Sphere "Sphere_Right" (
        active = true
        prepend apiSchemas = ["MaterialBindingAPI"]
    )
    {
        rel material:binding = </Root/GridMaterial/GridMaterial> (
            bindMaterialAs = "weakerThanDescendants"
        )
        double radius = 0.1
        quatf xformOp:orient = (1, 0, 0, 0)
        float3 xformOp:scale = (1, 1, 1)
        float3 xformOp:translate = (0.5, 1.5, -1.5)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]
    }

    def Sphere "Sphere_Left" (
        active = true
        prepend apiSchemas = ["MaterialBindingAPI"]
    )
    {
        rel material:binding = </Root/GridMaterial/GridMaterial> (
            bindMaterialAs = "weakerThanDescendants"
        )
        double radius = 0.1
        quatf xformOp:orient = (1, 0, 0, 0)
        float3 xformOp:scale = (1, 1, 1)
        float3 xformOp:translate = (-0.5, 1.5, -1.5)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]
    }

    def "GridMaterial" (
        active = true
        prepend references = @Materials/GridMaterial.usda@
    )
    {
        float3 xformOp:scale = (1, 1, 1)
        uniform token[] xformOpOrder = ["xformOp:translate", "xformOp:orient", "xformOp:scale"]
    }
}

